from fastapi import APIRouter, Form, Request, Depends, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import <PERSON><PERSON>2Templates
from sqlalchemy.orm import Session
from sqlalchemy import text
from db_config.db_models import User
from db_config.db import AUTH_SCHEMA
from auth.rbac import get_user_roles, get_db

import bcrypt
import logging

logger = logging.getLogger(__name__)

router = APIRouter()
templates = Jinja2Templates(directory="web/templates")

def hash_password_bcrypt(password: str) -> str:
    """
    Hash a password using bcrypt without salt (salt is generated automatically by bcrypt)
    """
    # Convert password to bytes
    password_bytes = password.encode('utf-8')

    # Generate a salt and hash the password
    # Using rounds=12 which is a good balance between security and performance
    hashed = bcrypt.hashpw(password_bytes, bcrypt.gensalt(rounds=12))

    # Convert the hash back to a string for storage
    return hashed.decode('utf-8')

def verify_password_bcrypt(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against a bcrypt hash
    """
    try:
        # Check if the hashed password is in bcrypt format
        if not hashed_password or not (hashed_password.startswith('$2b$') or
                                      hashed_password.startswith('$2a$') or
                                      hashed_password.startswith('$2y$')):
            return False

        # Convert inputs to bytes
        password_bytes = plain_password.encode('utf-8')
        hashed_bytes = hashed_password.encode('utf-8')

        # Verify the password
        result = bcrypt.checkpw(password_bytes, hashed_bytes)
        return result
    except Exception as e:
        logger.error(f"Error verifying password with bcrypt: {str(e)}")
        return False

def get_user_by_prefixed_username(db: Session, input_value: str, site_id: int = 1):
    # Format with site_id prefix for username
    site_id_prefix = f"{site_id}_"

    # Always create the prefixed username regardless of input type
    prefixed_username = f"{site_id_prefix}{input_value}"

    # Create a raw SQL query to find the user
    sql_query = text(f"""
        SELECT id, username, email, mobile, password, enabled, site_id
        FROM {AUTH_SCHEMA}.user
        WHERE site_id = :site_id
        AND enabled = 1
        AND username = :username
        LIMIT 1
    """)

    # Execute the query with parameters
    result = db.execute(sql_query, {"site_id": site_id, "username": prefixed_username})

    # Fetch the first row (if any)
    row = result.fetchone()

    # Convert the row to a User object if a row was found
    user = None
    if row:
        # Create a User object from the row
        user = User()
        user.id = row[0]
        user.username = row[1]
        user.email = row[2]
        user.mobile = row[3]
        user.password = row[4]
        user.enabled = row[5]
        user.site_id = row[6]
    return user


@router.get("/")
def login_page(request: Request):
    # If user is already logged in, redirect to home
    if request.session.get("user"):
        response = RedirectResponse(url="/home", status_code=303)
        response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, max-age=0"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        return response

    # Otherwise show login page with cache control headers
    response = templates.TemplateResponse("login.html", {"request": request})
    response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, max-age=0"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    return response

@router.post("/", response_class=HTMLResponse)
def login_post(request: Request, username: str = Form(...), password: str = Form(...), db: Session = Depends(get_db)):
    site_id = 1  # Default site_id, can be made configurable

    # Try to find user with the site_id prefix for username
    user = get_user_by_prefixed_username(db, username, site_id)

    if not user:
        return templates.TemplateResponse("login.html", {"request": request, "error": "User not found"})

    # Verify password using bcrypt
    password_match = verify_password_bcrypt(password, user.password)

    # If password doesn't match, return error
    if not password_match:
        return templates.TemplateResponse("login.html", {"request": request, "error": "Incorrect password"})

    # Set session data
    request.session["user"] = {
        "id": user.id,
        "username": user.username,
        "email": user.email
    }

    # Get user roles from database
    try:
        user_roles = get_user_roles(db, user.id)
        request.session["roles"] = user_roles
    except Exception as e:
        logger.error(f"Error getting roles for user {user.username}: {str(e)}")
        request.session["roles"] = []  # Default to no roles on error

    # Set cache control headers to prevent back navigation after logout
    response = RedirectResponse(url="/home", status_code=303)
    response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, max-age=0"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"

    return response

@router.get("/logout")
def logout(request: Request):
    # Clear session
    request.session.clear()

    # Redirect to login page with cache control headers
    response = RedirectResponse(url="/", status_code=303)
    response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, max-age=0"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"

    return response

@router.get("/pdf-text-extractor", response_class=HTMLResponse)
def pdf_text_extractor_page(request: Request):
    # Check if user is logged in
    if not request.session.get("user"):
        response = RedirectResponse(url="/", status_code=303)
        response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, max-age=0"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        return response

    # Render the PDF text extractor page
    response = templates.TemplateResponse("pdf_text_extractor.html", {"request": request})
    response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, max-age=0"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    return response