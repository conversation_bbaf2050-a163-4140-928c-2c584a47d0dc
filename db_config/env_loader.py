import os
import yaml
import logging
from typing import Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class EnvLoader:
    """
    Loads database configuration from environment variables and YAML file
    """
    _instance = None
    _schemas_config = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(EnvLoader, cls).__new__(cls)
            cls._instance._load_schemas_config()
        return cls._instance

    def _load_schemas_config(self):
        """
        Load schemas configuration from YAML file
        """
        schemas_path = Path(__file__).parent / "schemas.yaml"
        if schemas_path.exists():
            try:
                with open(schemas_path, 'r') as f:
                    self._schemas_config = yaml.safe_load(f)
            except Exception as e:
                logger.error(f"Error loading schemas configuration: {str(e)}")
                self._schemas_config = None
        else:
            self._schemas_config = None

    def get_schema_credentials(self, schema_name: str) -> Dict[str, Any]:
        """
        Get credentials and configuration for a specific schema from the YAML configuration
        """
        if not self._schemas_config or 'dataSources' not in self._schemas_config:
            logger.warning(f"No schema configuration found for {schema_name}")
            return {}

        # Try to get the schema-specific configuration
        schema_config = self._schemas_config['dataSources'].get(schema_name, {})

        # If not found, try the default dataSource
        if not schema_config and schema_name != 'dataSource':
            schema_config = self._schemas_config['dataSources'].get('dataSource', {})
            logger.warning(f"Using default dataSource for schema {schema_name}")

        return {
            'username': schema_config.get('username', ''),
            'password': schema_config.get('password', ''),
            'host': schema_config.get('host', '')
        }

    def get_database_config(self, schema_name: str = None) -> Dict[str, Any]:
        """
        Get database configuration from environment variables

        Expected environment variables:
        - DB_HOST: Database host
        - DB_PORT: Database port
        - DB_NAME: Database name

        Schema-specific credentials are loaded from the YAML file

        Returns a dictionary with the database configuration
        """
        # Use wsuser as the default schema if none specified
        if schema_name is None:
            # Import here to avoid circular import
            from db_config.db import AUTH_SCHEMA
            schema_name = os.environ.get("DB_SCHEMA", AUTH_SCHEMA)

        # Get schema-specific credentials
        schema_creds = self.get_schema_credentials(schema_name)

        # Debug: Print all environment variables
        logger.info("Environment variables:")
        for key, value in os.environ.items():
            if key.startswith("DB_"):
                masked_value = "********" if "PASSWORD" in key else value
                logger.info(f"  {key}={masked_value}")

        # Build configuration with environment variables and schema credentials
        # Use schema-specific host if available, otherwise fall back to environment variable or localhost
        schema_host = schema_creds.get('host')

        config = {
            "host": os.environ.get(f"DB_{schema_name.upper()}_HOST",
                    schema_host if schema_host else os.environ.get("DB_HOST", "localhost")),
            "port": int(os.environ.get("DB_PORT", "3306")),
            "database": os.environ.get(f"DB_{schema_name.upper()}_NAME", schema_name),
            "schema": schema_name,
            "username": os.environ.get(f"DB_{schema_name.upper()}_USERNAME", schema_creds.get('username', schema_name)),  # Default to schema name
            "password": os.environ.get(f"DB_{schema_name.upper()}_PASSWORD", schema_creds.get('password', ''))  # No default password
        }

        # Check for missing required configuration
        missing = [k for k, v in config.items() if not v and k != 'password']
        if missing:
            logger.warning(f"Missing database configuration: {', '.join(missing)}")

        # Log the configuration (without password)
        safe_config = config.copy()
        safe_config["password"] = "********" if safe_config["password"] else "<empty>"
        logger.info(f"Database configuration for schema {schema_name}: {safe_config}")

        return config

    def get_database_url(self, schema_name: str = None) -> str:
        """
        Get SQLAlchemy database URL for a specific schema
        """
        config = self.get_database_config(schema_name)

        # Check if we have the required configuration
        if not config.get("host") or not config.get("database") or not config.get("username"):
            logger.error(f"Missing required database configuration for schema {schema_name}")
            logger.error("Please set the required environment variables using set_db_env.sh")
            return ""

        # Use values from config
        host = config.get("host")
        database = config.get("database")
        port = config.get("port") or 3306
        username = config.get("username")
        password = config.get("password") or ""

        # Build the URL
        url = f"mysql+pymysql://{username}:{password}@{host}:{port}/{database}"
        logger.info(f"Database URL for schema {schema_name}: mysql+pymysql://{username}:********@{host}:{port}/{database}")

        return url

    def test_connection(self, schema_name: str = None) -> bool:
        """
        Test database connection for a specific schema
        """
        from sqlalchemy import create_engine, text
        from sqlalchemy.exc import SQLAlchemyError

        url = self.get_database_url(schema_name)
        logger.info(f"Testing connection for schema {schema_name} with URL: {url}")

        try:
            engine = create_engine(url)
            connection = engine.connect()
            connection.close()
            logger.info(f"Successfully connected to database schema: {schema_name}")
            return True
        except SQLAlchemyError as e:
            logger.error(f"Failed to connect to database schema {schema_name}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error testing connection for schema {schema_name}: {str(e)}")
            return False

# Create a singleton instance
env_loader = EnvLoader()
