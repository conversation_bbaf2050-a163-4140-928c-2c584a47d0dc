import logging
from logging.handlers import RotatingFileHandler
import sys
import os
from config import LOG_LEVEL, SHOW_LOGS, LOG_DIR

os.makedirs(LOG_DIR, exist_ok=True)

APP_LOG_FILE = os.path.join(LOG_DIR, "app.log")
ACCESS_LOG_FILE = os.path.join(LOG_DIR, "access.log")

def setup_logging():
    log_level = getattr(logging, LOG_LEVEL.upper(), logging.INFO)

    # ---- Application logger ----
    app_handler = RotatingFileHandler(APP_LOG_FILE, maxBytes=10*1024*1024, backupCount=1)
    formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(name)s - %(message)s')
    app_handler.setFormatter(formatter)

    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)

    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    root_logger.addHandler(app_handler)
    if SHOW_LOGS:
        root_logger.addHandler(console_handler)
