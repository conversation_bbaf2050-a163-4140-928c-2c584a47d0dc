"""
Progress tracking utility for long-running processes.
Uses Redis for persistence across server instances.
"""
import asyncio
import json
import logging
import time
import uuid
from typing import Dict, List, Optional, Any

# Import Redis client
from utils.redis_util import get_redis_client

logger = logging.getLogger(__name__)

class ProgressTracker:
    """
    Tracks progress of long-running tasks and provides updates to clients.
    Uses a singleton pattern to maintain state across requests.
    Uses Redis for persistence across server instances.
    """
    _instance = None
    _tasks: Dict[str, Dict[str, Any]] = {}
    _sse_clients: Dict[str, List[asyncio.Queue]] = {}
    _redis_prefix = "task_tracker:"

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ProgressTracker, cls).__new__(cls)
            # Initialize Redis client
            try:
                cls._redis = get_redis_client()
                # Load existing tasks from Redis
                cls._load_tasks_from_redis()
            except Exception as e:
                logger.error(f"Failed to initialize Redis client: {e}")
                cls._redis = None
        return cls._instance

    @classmethod
    def _load_tasks_from_redis(cls):
        """
        Load existing tasks from Redis into memory.
        Also cleans up completed or failed tasks during loading.
        """
        if not hasattr(cls, '_redis') or cls._redis is None:
            return

        try:
            # Get all task keys
            task_keys = cls._redis.keys(f"{cls._redis_prefix}*")
            logger.info(f"Found {len(task_keys)} tasks in Redis")

            # Track tasks to remove
            to_remove = []

            # Load each task
            for key in task_keys:
                try:
                    task_id = key.decode('utf-8').replace(cls._redis_prefix, "")
                    task_data = cls._redis.get(key)
                    if task_data:
                        task = json.loads(task_data)

                        # Check if task is completed or failed
                        if task.get('status') in ["completed", "failed"]:
                            # Add to removal list
                            to_remove.append(key)
                        else:
                            # Only load active tasks into memory
                            cls._tasks[task_id] = task
                except Exception as e:
                    logger.error(f"Error loading task from Redis: {e}")

            # Remove completed or failed tasks from Redis
            if to_remove:
                cls._redis.delete(*to_remove)

        except Exception as e:
            logger.error(f"Error loading tasks from Redis: {e}")

    @classmethod
    def create_task(cls, task_type: str = "extraction") -> str:
        """
        Create a new task and return its ID.

        Args:
            task_type: Type of task (e.g., "extraction")

        Returns:
            str: Task ID
        """
        task_id = str(uuid.uuid4())
        task_data = {
            "id": task_id,
            "type": task_type,
            "status": "created",
            "progress": 0,
            "current_step": "Initializing",
            "steps": [],
            "created_at": time.time(),
            "updated_at": time.time(),
            "completed_at": None,
            "result": None,
            "error": None
        }

        # Store in memory
        cls._tasks[task_id] = task_data

        # Initialize the SSE clients list for this task
        cls._sse_clients[task_id] = []

        # Store in Redis if available
        if hasattr(cls, '_redis') and cls._redis:
            try:
                redis_key = f"{cls._redis_prefix}{task_id}"
                cls._redis.set(redis_key, json.dumps(task_data))
                # Set expiration to 48 hours (172800 seconds)
                cls._redis.expire(redis_key, 172800)
            except Exception as e:
                logger.error(f"Failed to store task {task_id} in Redis: {e}")

        logger.info(f"Created task {task_id} of type {task_type}")
        return task_id

    @classmethod
    def update_task(cls, task_id: str, **kwargs) -> None:
        """
        Update task status and notify listeners.

        Args:
            task_id: Task ID
            **kwargs: Fields to update
        """
        # First try to get the task from memory or Redis
        task = cls.get_task(task_id)
        if not task:
            return

        # Make sure the task is in memory
        cls._tasks[task_id] = task

        # Update task fields
        for key, value in kwargs.items():
            if key in task:
                task[key] = value
            else:
                task[key] = value

        # Add step to history if provided
        if "current_step" in kwargs:
            task["steps"].append({
                "step": kwargs["current_step"],
                "time": time.time()
            })

        # Store updated task in Redis if available
        if hasattr(cls, '_redis') and cls._redis:
            try:
                redis_key = f"{cls._redis_prefix}{task_id}"
                cls._redis.set(redis_key, json.dumps(task))
                # Reset expiration to 48 hours (172800 seconds)
                cls._redis.expire(redis_key, 172800)
            except Exception as e:
                logger.error(f"Failed to update task {task_id} in Redis: {e}")

        # Always update the updated_at timestamp
        task["updated_at"] = time.time()

        # If status is completed or failed, set completed_at
        if kwargs.get("status") in ["completed", "failed"]:
            task["completed_at"] = time.time()

        # Notify all listeners
        cls._notify_listeners(task_id)

    @classmethod
    def add_step(cls, task_id: str, step: str, progress: Optional[int] = None) -> None:
        """
        Add a step to the task and update progress.

        Args:
            task_id: Task ID
            step: Description of the current step
            progress: Optional progress percentage (0-100)
        """
        # Check if the task exists
        if task_id not in cls._tasks:
            return

        # Create a step object with timestamp
        step_obj = {
            "step": step,
            "timestamp": time.time()
        }

        # Update the task
        update_data = {
            "current_step": step,
            "steps": cls._tasks[task_id].get("steps", []) + [step_obj]
        }

        if progress is not None:
            update_data["progress"] = progress

        # Update the task and notify listeners
        cls.update_task(task_id, **update_data)

    @classmethod
    def complete_task(cls, task_id: str, result: Any = None) -> None:
        """
        Mark a task as completed and remove from Redis.

        Args:
            task_id: Task ID
            result: Optional result data
        """
        # Update the task in memory
        cls.update_task(
            task_id,
            status="completed",
            progress=100,
            current_step="Completed",
            result=result
        )

        # Remove from Redis immediately if available
        if hasattr(cls, '_redis') and cls._redis:
            try:
                redis_key = f"{cls._redis_prefix}{task_id}"
                cls._redis.delete(redis_key)
            except Exception as e:
                logger.error(f"Failed to remove completed task {task_id} from Redis: {e}")

    @classmethod
    def fail_task(cls, task_id: str, error: str) -> None:
        """
        Mark a task as failed and remove from Redis.

        Args:
            task_id: Task ID
            error: Error message
        """
        # Update the task in memory
        cls.update_task(
            task_id,
            status="failed",
            current_step="Failed",
            error=error
        )

        # Remove from Redis immediately if available
        if hasattr(cls, '_redis') and cls._redis:
            try:
                redis_key = f"{cls._redis_prefix}{task_id}"
                cls._redis.delete(redis_key)
            except Exception as e:
                logger.error(f"Failed to remove failed task {task_id} from Redis: {e}")

    @classmethod
    def get_task(cls, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get task details. First checks in-memory cache, then falls back to Redis if available.

        Args:
            task_id: Task ID

        Returns:
            Dict or None: Task details if found
        """
        # First check in-memory cache
        task = cls._tasks.get(task_id)
        if task:
            return task

        # If not in memory and Redis is available, try to get from Redis
        if hasattr(cls, '_redis') and cls._redis:
            try:
                redis_key = f"{cls._redis_prefix}{task_id}"
                task_data = cls._redis.get(redis_key)
                if task_data:
                    # Parse the JSON data
                    task = json.loads(task_data)
                    # Store in memory for future access
                    cls._tasks[task_id] = task
                    return task
            except Exception as e:
                logger.error(f"Error retrieving task {task_id} from Redis: {e}")

        # Task not found in memory or Redis
        return None

    @classmethod
    def clean_old_tasks(cls, max_age_seconds: int = 3600) -> None:
        """
        Remove old completed tasks to prevent memory leaks.
        Cleans tasks from both memory and Redis.

        Args:
            max_age_seconds: Maximum age in seconds for completed tasks
        """
        current_time = time.time()
        to_remove = []

        # Find old tasks in memory
        for task_id, task in cls._tasks.items():
            if task["status"] in ["completed", "failed"]:
                if task["completed_at"] and (current_time - task["completed_at"]) > max_age_seconds:
                    to_remove.append(task_id)

        # Remove from memory
        for task_id in to_remove:
            del cls._tasks[task_id]
            if task_id in cls._sse_clients:
                del cls._sse_clients[task_id]

            # Remove from Redis if available
            if hasattr(cls, '_redis') and cls._redis:
                try:
                    redis_key = f"{cls._redis_prefix}{task_id}"
                    cls._redis.delete(redis_key)
                except Exception as e:
                    logger.error(f"Failed to remove task {task_id} from Redis: {e}")

    @classmethod
    def clean_all_completed_tasks(cls) -> Dict[str, Any]:
        """
        Remove all completed and failed tasks from memory and Redis.

        Returns:
            Dict with counts of removed tasks
        """
        to_remove_memory = []
        to_remove_redis = []

        # Find completed and failed tasks in memory
        for task_id, task in cls._tasks.items():
            if task["status"] in ["completed", "failed"]:
                to_remove_memory.append(task_id)

        # Remove from memory
        for task_id in to_remove_memory:
            del cls._tasks[task_id]
            if task_id in cls._sse_clients:
                del cls._sse_clients[task_id]

        # Find and remove completed and failed tasks in Redis
        if hasattr(cls, '_redis') and cls._redis:
            try:
                # Get all task keys
                task_keys = cls._redis.keys(f"{cls._redis_prefix}*")

                # Check each task
                for key in task_keys:
                    try:
                        task_data = cls._redis.get(key)
                        if task_data:
                            task = json.loads(task_data)
                            if task.get('status') in ["completed", "failed"]:
                                to_remove_redis.append(key)
                    except Exception as e:
                        logger.error(f"Error checking Redis task {key}: {e}")

                # Remove from Redis
                if to_remove_redis:
                    cls._redis.delete(*to_remove_redis)
            except Exception as e:
                logger.error(f"Error cleaning Redis tasks: {e}")

        return {
            "memory_tasks_removed": len(to_remove_memory),
            "redis_tasks_removed": len(to_remove_redis)
        }

    # These methods are deprecated and will be removed in a future version
    @classmethod
    async def listen_for_updates(cls, task_id: str) -> asyncio.Queue:
        """
        Create a queue to listen for task updates.

        Args:
            task_id: Task ID

        Returns:
            asyncio.Queue: Queue that will receive task updates
        """
        if task_id not in cls._tasks:
            queue = asyncio.Queue()
            await queue.put(json.dumps({"error": "Task not found"}))
            return queue

        queue = asyncio.Queue()

        # Send current state immediately
        await queue.put(json.dumps(cls._tasks[task_id]))

        # Register as an SSE client
        cls.register_sse_client(task_id, queue)

        return queue

    @classmethod
    def remove_listener(cls, task_id: str, queue: asyncio.Queue) -> None:
        """
        Remove a listener queue.

        Args:
            task_id: Task ID
            queue: Queue to remove
        """
        cls.unregister_sse_client(task_id, queue)

    @classmethod
    def register_sse_client(cls, task_id: str, queue: asyncio.Queue) -> None:
        """
        Register an SSE client queue for a task.

        Args:
            task_id: Task ID
            queue: Queue for SSE updates
        """
        if task_id not in cls._sse_clients:
            cls._sse_clients[task_id] = []
        cls._sse_clients[task_id].append(queue)

    @classmethod
    def unregister_sse_client(cls, task_id: str, queue: asyncio.Queue) -> None:
        """
        Unregister an SSE client queue.

        Args:
            task_id: Task ID
            queue: Queue to remove
        """
        if task_id in cls._sse_clients and queue in cls._sse_clients[task_id]:
            cls._sse_clients[task_id].remove(queue)

    @classmethod
    def _notify_listeners(cls, task_id: str) -> None:
        """
        Notify all listeners of a task update.

        Args:
            task_id: Task ID
        """
        # Check if the task exists
        if task_id not in cls._tasks:
            return

        task_data = cls._tasks[task_id]

        # Add a timestamp to the update
        task_data["updated_at"] = time.time()

        # Convert to JSON
        task_json = json.dumps(task_data)

        # Notify SSE clients
        if task_id in cls._sse_clients and cls._sse_clients[task_id]:
            # Log the notification for debugging
            client_count = len(cls._sse_clients[task_id])

            # Create a copy of the clients list to avoid modification during iteration
            clients = cls._sse_clients[task_id].copy()

            # Track successful notifications
            success_count = 0

            # Put the update in each client's queue
            for queue in clients:
                try:
                    # Try to put the update in the queue without blocking
                    if not queue.full():
                        queue.put_nowait(task_json)
                        success_count += 1
                    else:
                        # If the queue is full, try to remove an old item
                        try:
                            queue.get_nowait()
                            queue.put_nowait(task_json)
                            success_count += 1
                        except asyncio.QueueEmpty:
                            logger.warning(f"Queue full for SSE client of task {task_id}, skipping update")
                except Exception as e:
                    logger.error(f"Error notifying SSE client: {e}")
                    # If there's an error with this client, remove it from the list
                    try:
                        if queue in cls._sse_clients[task_id]:
                            cls._sse_clients[task_id].remove(queue)
                            logger.warning(f"Removed problematic SSE client for task {task_id}")
                    except Exception:
                        pass
        else:
            logger.info(f"No SSE clients to notify for task {task_id} - step: {task_data.get('current_step')}")

# Create a singleton instance
progress_tracker = ProgressTracker()
