"""
PDF Extraction Utilities

This module provides utilities for extracting content from PDF files,
including converting PDFs to images and extracting HTML and structured data.
"""

import os
import logging
import tempfile
import json
from pathlib import Path
from typing import Optional, List, Dict, Any
from utils.s3_utils import read_file_from_s3

from pdf2image import convert_from_bytes
from utils.openai_vision import extract_html_from_image, extract_text_from_image
from utils.enhanced_validator import EnhancedValidator
import config

# Initialize logger
logger = logging.getLogger(__name__)

def get_extraction_json_path(pdf_path: str) -> str:
    """
    Get the path to the JSON file that would store extracted content for a PDF.
    This function is kept for compatibility with the delete_document endpoint.

    Args:
        pdf_path (str): The path to the PDF file in S3

    Returns:
        str: The path to the JSON file
    """
    # Convert the PDF path to a JSON path with the same name but .json extension
    pdf_path_obj = Path(pdf_path)
    json_path = pdf_path_obj.with_suffix('.json')
    return str(json_path)

def convert_pdf_to_images(pdf_content: bytes, output_dir: str) -> List[str]:
    """
    Convert PDF content to images and save them to the specified directory.

    Args:
        pdf_content (bytes): The PDF file content
        output_dir (str): Directory to save the images

    Returns:
        List[str]: List of paths to the saved images
    """
    try:
        # Convert PDF to images
        images = convert_from_bytes(pdf_content, dpi=200)

        # Save each image
        image_paths = []
        for i, img in enumerate(images):
            img_path = os.path.join(output_dir, f"page_{i + 1}.png")
            img.save(img_path, "PNG")
            image_paths.append(img_path)
        return image_paths
    except Exception as e:
        logger.error(f"Error converting PDF to images: {e}")
        raise

def extract_and_validate_content(
    image_paths: List[str],
    extract_func=extract_html_from_image,
    language: str = "english",
    max_workers: int = 3,
    use_parallel: bool = True,
    timeout: int = 120
) -> Dict[str, Any]:
    """
    Extract content from images and validate it using the enhanced validator.

    Args:
        image_paths (List[str]): List of paths to the images
        extract_func: Function to extract content from images (default: extract_html_from_image)
        language (str): Language of the content (default: "english")
        max_workers (int): Maximum number of parallel workers (default: 3)
        use_parallel (bool): Whether to use parallel processing (default: True)
        timeout (int): Maximum time in seconds to wait for a single page processing (default: 120)

    Returns:
        Dict[str, Any]: Dictionary containing the extraction and validation results
    """
    try:
        # Initialize the enhanced validator
        validator = EnhancedValidator(
            model="gpt-4.1-mini",
            max_workers=max_workers,
            use_parallel=use_parallel
        )
        results = validator.process_images(
            image_paths=image_paths,
            extract_func=extract_func,
            language=language,
            timeout=timeout
        )

        return results
    except Exception as e:
        logger.error(f"Error during extraction and validation: {e}")
        raise



def extract_html_from_pdf(pdf_path: str) -> Optional[str]:
    """
    Extract HTML content from a PDF file using the Question Paper Extractor workflow.
    This converts PDF pages to images, uses OpenAI Vision to extract content,
    and validates the extracted content.

    Args:
        pdf_path (str): The path to the PDF file in S3

    Returns:
        Optional[str]: The extracted and validated HTML content, or None if extraction failed
    """
    try:
        # Get the full S3 path
        full_s3_path = os.path.join(config.S3_MOUNT_PATH, pdf_path)

        # Read the PDF file using the S3 utility function that handles sudo permissions
        pdf_content = read_file_from_s3(full_s3_path)

        if pdf_content is None:
            return None

        # Create a temporary directory for storing images
        with tempfile.TemporaryDirectory() as temp_dir:

            # Convert PDF to images
            image_paths = convert_pdf_to_images(pdf_content, temp_dir)

            # Extract and validate content
            results = extract_and_validate_content(
                image_paths=image_paths,
                extract_func=extract_html_from_image,
                language="english",
                max_workers=3,
                use_parallel=True,
                timeout=120
            )

            # Extract the corrected HTML for each page
            page_htmls = []
            for i, path in enumerate(image_paths):
                page_key = f"page_{i + 1}"
                try:
                    page_result = results["page_results"].get(page_key, {})
                    if not page_result:
                        continue

                    # Use the corrected text if available, otherwise use the extracted text
                    corrected_text = page_result.get("corrected_text", "")
                    if not corrected_text:
                        corrected_text = page_result.get("extracted_text", "")

                    # Add the HTML to the list with page wrapper
                    page_htmls.append(f"<div class='pdf-page' data-page='{i+1}'>{corrected_text}</div>")

                except Exception as e:
                    logger.error(f"Error processing results for page {i+1}: {str(e)}")
                    # Continue with other pages even if one fails

            # Combine all HTML parts
            if page_htmls:
                html = "<div class='pdf-content'>" + "".join(page_htmls) + "</div>"
                return html
            else:
                return None
    except Exception as e:
        logger.error(f"Error extracting HTML from PDF: {e}")
        return None

def clean_extracted_json(raw_text: str) -> str:
    """
    Cleans extracted raw text to be valid JSON.
    Removes markdown-style blocks, extra 'json' text, trims spaces.

    Args:
        raw_text (str): Raw extracted text.

    Returns:
        str: Cleaned JSON string ready for json.loads
    """
    cleaned = raw_text.strip()

    if cleaned.startswith("```json"):
        cleaned = cleaned[7:]
    elif cleaned.startswith("```"):
        cleaned = cleaned[3:]
    if cleaned.endswith("```"):
        cleaned = cleaned[:-3]

    # Now remove leading "json" keyword if mistakenly present
    if cleaned.lower().startswith("json"):
        cleaned = cleaned[4:].strip()

    return cleaned

def extract_questions_from_pdf(pdf_path: str) -> Optional[List[Dict[str, Any]]]:
    """
    Extract questions from a PDF file using the Question Paper Extractor workflow.

    Args:
        pdf_path (str): The path to the PDF file in S3

    Returns:
        Optional[List[Dict[str, Any]]]: A list of extracted questions, or None if extraction failed
    """
    try:
        full_s3_path = os.path.join(config.S3_MOUNT_PATH, pdf_path)

        # Read the PDF file using the S3 utility function that handles sudo permissions
        pdf_content = read_file_from_s3(full_s3_path)

        if pdf_content is None:
            return None

        with tempfile.TemporaryDirectory() as temp_dir:

            image_paths = convert_pdf_to_images(pdf_content, temp_dir)

            results = extract_and_validate_content(
                image_paths=image_paths,
                extract_func=extract_text_from_image,
                language="english",
                max_workers=3,
                use_parallel=True,
                timeout=120
            )

            all_questions = []

            for i, path in enumerate(image_paths):
                page_key = f"page_{i + 1}"

                page_result = results["page_results"].get(page_key, {})
                if not page_result:
                    continue

                corrected_text = page_result.get("corrected_text", "") or page_result.get("extracted_text", "")
                if not corrected_text.strip():
                    continue
                try:
                    cleaned_text = clean_extracted_json(corrected_text)

                    questions_data = json.loads(cleaned_text)

                    page_questions = []
                    if isinstance(questions_data, dict) and "questions" in questions_data:
                        page_questions = questions_data["questions"]
                    elif isinstance(questions_data, list):
                        page_questions = questions_data
                    else:
                        logger.warning(f"Unexpected JSON structure on page {i+1}: {type(questions_data)}")

                    for q in page_questions:
                        q["page_number"] = i + 1

                    all_questions.extend(page_questions)

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON from page {i+1}: {e}")
                    logger.info(f"Full unparsed text from page {i+1}:\n{corrected_text}")
                    continue  # Move to next page

            # Format the extracted questions
            formatted_questions = []
            for idx, q in enumerate(all_questions):
                try:

                    if not q.get("question_text", "").strip():
                        continue

                    has_options = bool(q.get("options")) and isinstance(q["options"], list) and len(q["options"]) > 0

                    formatted_question = {
                        "question": q.get("question_text", ""),
                        "question_type": "MCQ" if has_options else "Descriptive",
                        "marks": float(q.get("marks", 1.0)) if q.get("marks") else 1.0,
                        "negative_mark": float(q.get("negative_mark", 0.0)) if q.get("negative_mark") else 0.0,
                        "topic": q.get("topic", ""),
                        "subtopic": q.get("subtopic", "")
                    }

                    if has_options:
                        for j, option_obj in enumerate(q["options"][:5]):
                            formatted_question[f"option{j+1}"] = option_obj.get("option_text", "").strip()
                        for j in range(len(q["options"]), 5):
                            formatted_question[f"option{j+1}"] = ""
                    else:
                        for j in range(1, 6):
                            formatted_question[f"option{j}"] = ""

                    if "answer" in q:
                        formatted_question["answer"] = q["answer"]

                    formatted_questions.append(formatted_question)

                except Exception as e:
                    logger.error(f"Error formatting question {idx+1}: {e}")
                    continue

            if not formatted_questions:
                return None
            return formatted_questions

    except Exception as e:
        logger.error(f"Fatal error extracting questions from PDF: {e}")
        return None