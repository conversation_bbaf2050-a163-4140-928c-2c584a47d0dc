# pdf_utils.py

import os
import io
import tempfile
import asyncio
import time
import traceback

import fitz
import logging
from config import S3_MOUNT_PATH
from bs4 import BeautifulSoup
from PIL import Image
from utils.s3_utils import read_file_from_s3, get_s3_path
from db_config.db import get_session, CONTENT_SCHEMA
from sqlalchemy import text


logger = logging.getLogger(__name__)


def extract_text_from_pdf(file_data):
    """
    Function to extract text from a PDF file data in bytes.
    It creates a file-like object from the bytes, opens the PDF file, and extracts text from each page.

    Args:
        file_data (bytes): The data of the PDF file in bytes.

    Returns:
        str: The extracted text from the PDF file.
    """
    # Create a file-like object from the bytes
    file_like_object = io.BytesIO(file_data)

    # Open the PDF
    text = ''
    # Open the PDF file
    try:
        doc = fitz.open(stream=file_like_object, filetype="pdf")
        # Extract text from each page
        for page_num in range(len(doc)):
            try:
                page = doc.load_page(page_num)
                page_text = page.get_text("text")
                if page_text:
                    text += page_text
            except Exception as e:
                logger.error(f"Error extracting text from page {page_num + 1}: {e}")
    except Exception as e:
        logger.error(f"Error opening PDF: {e}")

    return text


async def extract_text_from_pdf_new(res_id: str):
    """
    Extract text from a PDF resource given its resource ID.

    This function first checks if the extracted text file already exists at the path:
    'supload/pdfextracts/<bookId>/<chapterId>/<resId>/extractedImages/<resId>.txt'

    If the file exists, it returns the text from the file.
    If not, it calls the PDF text extraction process to create the file and then returns the text.

    Args:
        res_id (str): The resource ID to process.

    Returns:
        str: The extracted text from the PDF, or None if an error occurs.
    """
    try:
        # Get a database session for the wscontent schema
        db_session = next(get_session(CONTENT_SCHEMA))
        try:
            # Query the resource_dtl table to get chapter_id
            resource_query = text("""
                SELECT id, chapter_id
                FROM wscontent.resource_dtl
                WHERE id = :res_id
                LIMIT 1
            """)

            resource_result = db_session.execute(resource_query, {"res_id": res_id})
            resource_row = resource_result.fetchone()

            if not resource_row:
                return None

            # Extract resource details
            resource_id = resource_row[0]
            chapter_id = resource_row[1]

            # Query the chapters_mst table to get the book_id
            chapter_query = text("""
                SELECT book_id
                FROM wscontent.chapters_mst
                WHERE id = :chapter_id
                LIMIT 1
            """)

            chapter_result = db_session.execute(chapter_query, {"chapter_id": chapter_id})
            chapter_row = chapter_result.fetchone()

            if not chapter_row:
                return None

            # Extract book_id
            book_id = chapter_row[0]
        finally:
            # Close the database session
            db_session.close()

        # Construct the path to the text file
        text_file_path = f"supload/pdfextracts/{book_id}/{chapter_id}/{resource_id}/extractedImages/{resource_id}.txt"

        # Check if the file exists in S3
        full_s3_path = get_s3_path(text_file_path)
        content = read_file_from_s3(full_s3_path)

        if content is not None:
            # File exists, return the text
            content_str = content.decode("utf-8")
            return content_str
        else:
            # Import here to avoid circular imports
            from agents.pdf_text_extractor import PDFTextExtractor

            # Initialize the PDF text extractor
            extractor = PDFTextExtractor()

            result = await extractor.process_pdf_text_extraction(res_id)
            if result["status"] != "success":
                return None

            # Now read the extracted text
            read_result = await extractor.read_extracted_text(res_id)

            if read_result["status"] == "success":
                return read_result["content"]
            else:
                return None

    except Exception as e:
        logger.error(f"Error in extract_text_from_pdf_new: {e}")
        logger.error(traceback.format_exc())
        return None


def extract_image_urls(content):
    """
    Extracts text and image URLs directly from the input HTML-like MCQ content.
    """
    base_url = "https://www.wonderslate.com"
    soup = BeautifulSoup(content, "html.parser")

    # Extract question text
    question_text = soup.find(text=True, recursive=False).strip()

    # Extract images and their corresponding text
    options = []
    for idx, img_tag in enumerate(soup.find_all("img")):
        parent_tag = img_tag.find_parent()  # Find the closest parent tag containing text
        text = parent_tag.get_text(strip=True) if parent_tag else "No text found"

        image_url = img_tag["src"]

        # Append base URL if the image URL does not start with "https"
        if not image_url.startswith("https"):
            image_url = base_url + image_url
        options.append({"text": f"Option {idx + 1}: {text}", "image_url": image_url})

    return question_text, options


def convert_pdf_to_images_and_save(pdf_path: str, output_dir: str = "output/pdf_images", zoom: float = 2.0) -> list[str]:
    """
    Converts each page of a PDF to an image and saves them locally.

    Args:
        pdf_path (str): Path to the input PDF file.
        output_dir (str): Directory to store the output images.
        zoom (float): Zoom factor to improve image quality (default = 2.0).

    Returns:
        List[str]: List of saved image file paths.
    """
    try:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        # Open the PDF
        pdf_document = fitz.open(pdf_path)
        image_paths = []

        for page_number in range(pdf_document.page_count):
            page = pdf_document[page_number]
            matrix = fitz.Matrix(zoom, zoom)
            pix = page.get_pixmap(matrix=matrix)

            # Create PIL image
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

            # Create unique image file name
            filename = f"page_{page_number + 1}.png"
            save_path = os.path.join(output_dir, filename)

            # Save image
            img.save(save_path, format="PNG")
            image_paths.append(save_path)

        pdf_document.close()
        return image_paths

    except Exception as e:
        raise RuntimeError(f"Failed to convert PDF to images: {str(e)}")


def get_pdf_path(res_id: str) -> str:
    """
    Get the PDF path (res_link) for a given resource ID from wscontent.resource_dtl table.

    Args:
        res_id (str): The resource ID to query

    Returns:
        str: The res_link value from the database

    Raises:
        Exception: If resource not found or res_link is null/empty
    """
    try:
        # Get a database session for the wscontent schema
        db_session = next(get_session(CONTENT_SCHEMA))
        try:
            # Query the resource_dtl table to get res_link
            resource_query = text("""
                SELECT res_link
                FROM wscontent.resource_dtl
                WHERE id = :res_id
                LIMIT 1
            """)

            resource_result = db_session.execute(resource_query, {"res_id": res_id})
            resource_row = resource_result.fetchone()

            if not resource_row:
                raise Exception(f"Resource with ID {res_id} not found in database")

            # Extract res_link
            res_link = resource_row[0]

            if res_link is None or res_link.strip() == "":
                raise Exception(f"res_link is null or empty for resource ID {res_id}")
            return res_link

        finally:
            # Close the database session
            db_session.close()
    except Exception as e:
        logger.error(f"Error in get_pdf_path for resource ID {res_id}: {e}")
        raise


def aggregate_data_from_json_objects(json_objects: list) -> dict:
    """
    Aggregates mcqs, answer_keys, and explanations from a list of parsed JSON objects.
    """
    all_mcqs = []
    all_answer_keys = {}
    all_explanations = []
    obj_count = 0
    for obj in json_objects:
        obj_count += 1
        if isinstance(obj, dict):
            # Aggregate MCQs
            mcqs = obj.get("mcqs")
            if isinstance(mcqs, list):
                all_mcqs.extend(mcqs)

            # Aggregate Answer Keys (handle potential collisions - last key wins)
            answer_keys = obj.get("answer_keys")
            if isinstance(answer_keys, dict):
                all_answer_keys.update(answer_keys)

            # Aggregate Explanations
            explanations = obj.get("explanations")
            if isinstance(explanations, list):
                all_explanations.extend(explanations)
    return {
        "mcqs": all_mcqs,
        "answer_keys": all_answer_keys,
        "explanations": all_explanations,
    }
