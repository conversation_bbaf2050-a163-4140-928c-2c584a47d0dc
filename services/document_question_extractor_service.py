"""
Document Question Extractor Service

This service handles the extraction of questions from document PDFs.
It converts PDFs to images, extracts text, parses questions using LLM,
and saves the results to the database.
"""

import logging
import os
import json
import math
import uuid
import asyncio
import tempfile
import traceback

import config
from agents.utils.pdf_helpers import PDFImageConverter
from agents.core.extractor import ExtractorAgent
from agents.schemas.agent_prompts import mcq_text_extractor_prompt, qp_parser_prompt, qp_parser_dual_language_prompt, pre_process_prompt, fix_json_prompt
from agents.schemas.agent_prompts import mcq_text_extractor_prompt, qp_parser_prompt, pre_process_prompt, fix_json_prompt
from services.mcq_text_extractor_service import MCQTextExtractorService
from services.request_tracker_service import RequestTrackerService
from utils.s3_utils import upload_file_to_s3, read_file_from_s3, get_s3_path, list_files_in_s3_directory
from db_config.pyqs_admin_db import create_exam_solution, update_exam_document_extracted_content_status
from llm_manager.llm_factory import LLMFactory
from config import llm_config
from langchain_core.messages import HumanMessage
from llm_manager.prompts import pre_process_explanation_prompt
import ast

# Get logger instance
logger = logging.getLogger(__name__)

# Initialize LLM factory
llm_factory = LLMFactory(llm_config)


class DocumentQuestionExtractorService:
    """
    Service for extracting questions from document PDFs.
    """

    def __init__(self, max_concurrent_extractions=None):
        """
        Initialize the DocumentQuestionExtractorService.
        """
        self.max_concurrent_extractions = max_concurrent_extractions or getattr(config, 'MAX_CONCURRENT_EXTRACTIONS', 2)
        self.pdf_zoom_factor = getattr(config, 'PDF_ZOOM_FACTOR', 1.5)
        self.question_batch_size = getattr(config, 'MCQ_BATCH_SIZE', 10)
        
        # Delay LLM initialization until first use
        self.llm = None
        self._llm_initialized = False
        
        # Initialize request tracker
        self.tracker_service = RequestTrackerService()

    def _initialize_llm(self):
        """Initialize LLM if not already initialized."""
        if not self._llm_initialized:
            self.llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini", req_timeout=180)
            self._llm_initialized = True

    async def extract_questions_from_document(self, document_id, question_paper_path, total_questions, username=None, dual_language=False):
        """
        Extract questions from a document PDF.
        """
        # Create a task for tracking
        task_id = self.tracker_service.create_task("document_question_extraction")
        
        if not task_id:
            return {"status": "error", "message": "Failed to create tracking task"}

        # Start the extraction process in background
        asyncio.create_task(self._extract_questions_background(
            task_id, document_id, question_paper_path, total_questions, username, dual_language
        ))
        
        return {"status": "success", "task_id": task_id}

    async def _extract_questions_background(self, task_id, document_id, question_paper_path, total_questions, username=None, dual_language=False):
        """
        Background task for extracting questions from document.
        """
        try:
            # Update task status to in progress
            self.tracker_service.update_task_status(task_id, "IN_PROGRESS")
            
            # Step 1: Convert PDF to images
            try:
                # Convert PDF to images
                pdf_converter = PDFImageConverter()
                conversion_result = pdf_converter.convert_and_upload(
                    pdf_path=question_paper_path,
                    book_id="documents",
                    chapter_id=str(document_id),
                    res_id="main",
                    zoom=self.pdf_zoom_factor
                )
                
                if conversion_result["status"] != "success":
                    raise Exception(f"PDF conversion failed: {conversion_result['message']}")
                
                # Get column image URLs
                col_img_urls = conversion_result.get("cropped_image_urls", [])
                if not col_img_urls:
                    raise Exception("No column images were generated")

                # Step 2: Extract text from images in parallel
                text_extractor = MCQTextExtractorService()
                text_files = await text_extractor._extract_text_parallel(
                    col_img_urls, task_id, "main", str(document_id), "documents"
                )
                
                if not text_files:
                    raise Exception("No text files were created")
                
                # Step 3: Merge all text files into one combined file
                s3_combined_path = await text_extractor._merge_text_files(
                    text_files, str(document_id), "main", task_id
                )

                if not s3_combined_path:
                    raise Exception("Failed to create combined text file")

                # The combined file is already in S3, no need to upload again
                full_s3_path = s3_combined_path
                
                # Step 5: Process question parsing in batches
                questions = await self._process_question_parsing_batches(
                    full_s3_path, total_questions, document_id, task_id, dual_language
                )
                
                if not questions:
                    raise Exception("No questions were extracted")

                # Step 6: Extract MCQ images from page images
                images_from_content = self._extract_quiz_images(
                    "main", str(document_id), "documents",
                    conversion_result.get("image_urls", []),
                    task_id,
                    explanation_start_page=None  # Skip explanation extraction for document questions
                )

                # Map images to questions (simple approach)
                if images_from_content:
                    questions = self._append_images_to_questions(questions, images_from_content, task_id)

                # Step 7: Save questions to database
                saved_count = await self._save_questions_to_database(questions, document_id, username, task_id)
                
                # Update task status to completed
                result_data = {
                    "document_id": document_id,
                    "total_questions_extracted": len(questions),
                    "total_questions_saved": saved_count,
                    "s3_text_path": full_s3_path
                }
                
                self.tracker_service.update_task_status(
                    task_id, "COMPLETED",
                    result_data=json.dumps(result_data)
                )

                # Update document extracted_content status to indicate questions have been extracted
                try:
                    update_success = update_exam_document_extracted_content_status(document_id, "EXTRACTED_ONLY_QUESTIONS")
                except Exception as status_update_error:
                    logger.error(f"[TASK:{task_id}] Error updating document status: {status_update_error}")

                # Enhancement 2: Delete local folder after completion
                await self._cleanup_local_folder(document_id, task_id)

                # Clean up text files after successful processing
                try:
                    if text_files:
                        for text_file in text_files:
                            if os.path.exists(text_file):
                                os.unlink(text_file)
                    # Combined file is now stored directly in S3, no local cleanup needed
                except Exception as cleanup_error:
                    logger.warning(f"[TASK:{task_id}] Error during cleanup: {cleanup_error}")

            except Exception as inner_e:
                # Clean up files on error as well
                try:
                    if 'text_files' in locals() and text_files:
                        for text_file in text_files:
                            if os.path.exists(text_file):
                                os.unlink(text_file)

                    # Combined file is now stored directly in S3, no local cleanup needed

                    # Also cleanup local folder on error
                    await self._cleanup_local_folder(document_id, task_id)
                except:
                    pass
                raise inner_e
                
        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error in document question extraction: {e}")
            logger.error(traceback.format_exc())
            
            # Update task status to failed
            self.tracker_service.update_task_status(task_id, "FAILED", error_message=str(e))

    async def _process_question_parsing_batches(self, s3_text_path, total_questions, document_id, task_id, dual_language=False):
        """Process question parsing in batches and combine results."""
        try:
            # Use configurable batch size
            batch_size = self.question_batch_size
            num_batches = math.ceil(total_questions / batch_size)

            # Read text content from S3
            full_s3_path = get_s3_path(s3_text_path)
            content = read_file_from_s3(full_s3_path)
            if content is None:
                return []

            # Convert bytes to string
            text_content = content.decode("utf-8")

            # Create output directory for JSON files in a temporary location
            import tempfile
            json_output_dir = os.path.join(tempfile.gettempdir(), f"json_batches_{task_id}")
            os.makedirs(json_output_dir, exist_ok=True)

            batch_json_files = []
            failed_batches = []

            # Process each batch
            for batch_num in range(1, num_batches + 1):
                start_question = (batch_num - 1) * batch_size + 1
                end_question = min(batch_num * batch_size, total_questions)

                # Call LLM for this batch
                batch_json_content = await self._call_llm_for_batch(
                    text_content, start_question, end_question, task_id, dual_language
                )

                if batch_json_content:
                    # Save batch JSON file
                    batch_json_filename = f"batch_{batch_num}_{start_question}_{end_question}.json"
                    batch_json_path = os.path.join(json_output_dir, batch_json_filename)

                    with open(batch_json_path, "w", encoding="utf-8") as f:
                        json.dump(batch_json_content, f, indent=2, ensure_ascii=False)

                    batch_json_files.append(batch_json_path)
                else:
                    failed_batches.append(batch_num)
                    logger.error(f"[TASK:{task_id}] Batch {batch_num} failed")

            # Combine all batch JSON files
            combined_questions = await self._combine_json_files(batch_json_files, document_id, task_id)

            # Enhancement 1: Upload combined JSON to S3
            if combined_questions:
                await self._upload_combined_json_to_s3(combined_questions, document_id, task_id)

            # Clean up batch JSON files
            for batch_file in batch_json_files:
                try:
                    os.unlink(batch_file)
                except:
                    pass

            # Clean up batch directory
            try:
                os.rmdir(json_output_dir)
            except:
                pass

            return combined_questions

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error in question parsing batches: {e}")
            logger.error(traceback.format_exc())
            return []

    async def _call_llm_for_batch(self, text_content, start_question, end_question, task_id, dual_language=False):
        """Call LLM to parse questions for a specific batch."""
        try:
            # Initialize LLM if needed
            self._initialize_llm()

            # Create the prompt using appropriate prompt based on dual_language flag
            if dual_language:
                prompt = qp_parser_dual_language_prompt(start_question, end_question, text_content)
            else:
                prompt = qp_parser_prompt(start_question, end_question, text_content)

            # Create message
            message = HumanMessage(content=prompt)

            try:
                response = await self.llm.ainvoke([message])
                response_content = response.content.strip()

                # Validate and fix JSON if needed
                json_response = await self._validate_and_fix_json_response(
                    response_content, start_question, task_id
                )

                if json_response is not None:
                    return json_response
                else:
                    return None

            except Exception as e:
                logger.warning(f"[TASK:{task_id}] LLM call failed on: {e}")
                return None

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error calling LLM for batch {start_question}-{end_question}: {e}")
            logger.error(traceback.format_exc())
            return None

    def _validate_json(self, json_content: str, task_id: str) -> bool:
        """
        Validate if the given content is valid JSON.

        Args:
            json_content: JSON string to validate
            task_id: Task ID for logging

        Returns:
            bool: True if valid JSON, False otherwise
        """
        try:
            json.loads(json_content)
            return True
        except json.JSONDecodeError as e:
            logger.warning(f"[TASK:{task_id}] JSON validation failed: {e}")
            return False
        except Exception as e:
            logger.warning(f"[TASK:{task_id}] JSON validation error: {e}")
            return False

    async def _fix_json_with_llm(self, invalid_json: str, task_id: str) -> str:
        """
        Use LLM to fix invalid JSON content.

        Args:
            invalid_json: Invalid JSON string to fix
            task_id: Task ID for logging

        Returns:
            str: Fixed JSON string, or None if failed
        """
        try:
            # Initialize LLM if needed
            self._initialize_llm()

            # Create the prompt using fix_json_prompt
            prompt_text = fix_json_prompt(invalid_json)

            # Call LLM
            response = await self.llm.ainvoke([HumanMessage(content=prompt_text)])

            # Extract content from response
            fixed_json_content = response.content.strip()
            return fixed_json_content

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error calling LLM to fix JSON: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _validate_and_fix_json_response(self, json_content: str, batch_num: int, task_id: str) -> dict:
        """
        Validate JSON content and fix it using LLM if invalid.

        Args:
            json_content: JSON string to validate and potentially fix
            batch_num: Batch number for logging
            task_id: Task ID for logging

        Returns:
            dict: Valid JSON object, or None if cannot be fixed
        """
        # First, try to validate the original JSON
        if self._validate_json(json_content, task_id):
            try:
                return json.loads(json_content)
            except json.JSONDecodeError:
                # This shouldn't happen since we just validated it, but just in case
                logger.error(f"[TASK:{task_id}] Unexpected JSON parse error after validation")
                return None

        # Try to fix the JSON using LLM
        fixed_json = await self._fix_json_with_llm(json_content, task_id)

        if fixed_json is None:
            return None

        # Validate the fixed JSON
        if self._validate_json(fixed_json, task_id):
            try:
                return json.loads(fixed_json)
            except json.JSONDecodeError:
                logger.error(f"[TASK:{task_id}] Unexpected JSON parse error after LLM fix")
                return None
        else:
            logger.error(f"[TASK:{task_id}] LLM-fixed JSON for batch {batch_num} is still invalid, skipping batch")
            return None

    async def _combine_json_files(self, batch_json_files, document_id, task_id):
        """Combine all batch JSON files into one list of questions."""
        try:
            combined_questions = []

            for json_file_path in batch_json_files:
                try:
                    with open(json_file_path, "r", encoding="utf-8") as f:
                        batch_data = json.load(f)

                    # Extract questions from the batch
                    questions = batch_data.get("questions", [])
                    if questions:
                        combined_questions.extend(questions)
                except Exception as e:
                    logger.error(f"[TASK:{task_id}] Error reading batch file {json_file_path}: {e}")
            return combined_questions

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error combining JSON files: {e}")
            logger.error(traceback.format_exc())
            return []

    async def _upload_combined_json_to_s3(self, combined_questions, document_id, task_id):
        """Upload combined JSON questions to S3."""
        try:
            # Create combined JSON structure
            combined_json = {
                "questions": combined_questions,
                "total_questions": len(combined_questions),
                "document_id": document_id,
                "extraction_timestamp": str(uuid.uuid4())
            }

            # Create temporary file for JSON
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as temp_file:
                json.dump(combined_json, temp_file, indent=2, ensure_ascii=False)
                temp_json_path = temp_file.name

            try:
                # Upload JSON file to S3 (same pattern as text file upload)
                s3_upload_result = upload_file_to_s3(
                    local_file_path=temp_json_path,
                    book_id="documents",
                    chapter_id=str(document_id),
                    res_id="main",
                    file_name=f"{document_id}_questions.json",
                    is_quiz_image=False
                )

            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_json_path)
                except:
                    pass

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error uploading combined JSON to S3: {e}")
            logger.error(traceback.format_exc())

    async def _save_questions_to_database(self, questions, document_id, username, task_id):
        """Save extracted questions to the database."""
        try:
            saved_count = 0
            skipped_count = 0

            for question_data in questions:
                try:
                    # Validate question text before saving
                    question_text = question_data.get("question", "")
                    if question_text is None or question_text.strip() == "":
                        skipped_count += 1
                        continue

                    # Prepare solution data for database
                    solution_data = {
                        "exam_dtl_id": document_id,
                        "question": question_text,
                        "question_type": question_data.get("question_type", "MCQ"),
                        "option1": question_data.get("option1", ""),
                        "option2": question_data.get("option2", ""),
                        "option3": question_data.get("option3", ""),
                        "option4": question_data.get("option4", ""),
                        "option5": question_data.get("option5", ""),
                        "answer": "",  # No answer in question paper
                        "marks": self._parse_numeric_field(question_data.get("marks")),
                        "negative_mark": self._parse_numeric_field(question_data.get("negative_mark")),
                        "topic": "",
                        "subtopic": "",
                        # Enhancement 3: Include directions if present
                        "directions": question_data.get("directions", "")
                    }

                    # Create the solution in database
                    solution_id = create_exam_solution(solution_data, username)
                    if solution_id:
                        saved_count += 1
                except Exception as e:
                    logger.error(f"[TASK:{task_id}] Error saving individual question: {e}")
            return saved_count

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error saving questions to database: {e}")
            logger.error(traceback.format_exc())
            return 0

    def _parse_numeric_field(self, value):
        """Parse a numeric field, handling various input types."""
        if value is None or value == "":
            return None

        try:
            return float(value)
        except (ValueError, TypeError):
            return None

    async def _cleanup_local_folder(self, document_id, task_id):
        """Clean up the local folder for the document."""
        try:
            import shutil

            # Path to the local folder: agents/local_page_images/documents/{document_id}
            local_folder_path = os.path.join("agents", "local_page_images", "documents", str(document_id))

            if os.path.exists(local_folder_path):
                shutil.rmtree(local_folder_path)
        except Exception as e:
            logger.warning(f"[TASK:{task_id}] Error cleaning up local folder: {e}")

    def _extract_quiz_images(self, res_id: str, chapter_id: str, book_id: str, image_urls, task_id: str, explanation_start_page: int = None):
        """
        Extract quiz images from PDF pages and return the list of extracted image URLs.

        Args:
            res_id: Resource ID
            chapter_id: Chapter ID
            book_id: Book ID
            image_urls: List of page image URLs (now S3 paths)
            task_id: Task ID for logging
            explanation_start_page: Page number from which explanations start (None to skip explanation extraction)

        Returns:
            List[str]: List of extracted quiz image URLs
        """
        try:
            # If explanation_start_page is None, skip explanation extraction entirely
            skip_explanation_extraction = explanation_start_page is None
            img_extractor = ExtractorAgent()
            all_extracted_img_urls = []
            start_idx = 0

            for idx in range(start_idx, len(image_urls)):
                page_img_url = image_urls[idx]
                page_name = page_img_url.split('/')[-1]
                page_id = page_name.split('.')[0]

                # Since images are now in S3, get the S3 image paths that match this page
                # Look for images that end with the page_id pattern (e.g., page_1_col_1.png, page_1_col_2.png)
                all_s3_images = list_files_in_s3_directory(
                    book_id=str(book_id),
                    chapter_id=str(chapter_id),
                    res_id=str(res_id),
                    subfolder="",  # Root folder where page images are stored
                    file_pattern="*.png"
                )

                # Filter images that match this page_id
                mcq_s3_image_paths = [
                    img_path for img_path in all_s3_images
                    if img_path.split('/')[-1].lower().endswith(f"{page_id}.png")
                ]
                mcq_s3_image_paths.sort(key=lambda x: self._natural_sort_key(os.path.basename(x)))

                if mcq_s3_image_paths:
                    page_number = int(page_id.split('_')[1])

                    # Determine which prompt to use based on explanation extraction settings
                    if skip_explanation_extraction:
                        # Always use regular prompt when explanation extraction is disabled
                        prompt = pre_process_prompt
                    else:
                        # Convert explanation_start_page to int if it's a string
                        if isinstance(explanation_start_page, str):
                            try:
                                explanation_start_page = int(explanation_start_page)
                            except ValueError:
                                logger.warning(f"[TASK:{task_id}] Invalid explanation_start_page value: {explanation_start_page}, using default value 5")
                                explanation_start_page = 5

                        if page_number >= explanation_start_page:
                            prompt = pre_process_explanation_prompt
                        else:
                            prompt = pre_process_prompt

                    prompt_content = img_extractor.construct_input_content(prompt, mcq_s3_image_paths)

                    # Ensure LLM is initialized before use
                    self._initialize_llm()
                    response = self.llm.invoke([HumanMessage(content=prompt_content, additional_kwargs={"tool_choice": "vision"})])

                    try:
                        # Try JSON load first
                        parsed = json.loads(response.content)
                    except json.JSONDecodeError:
                        # If it fails, try ast.literal_eval
                        parsed = ast.literal_eval(response.content)

                    mappings = parsed.get("mappings", [])

                    # Only extract explanation mappings if explanation extraction is enabled
                    if skip_explanation_extraction:
                        explanation_mappings = []
                    else:
                        explanation_mappings = parsed.get("explanationMappings", [])

                    extracted_img_urls = img_extractor.extract_quiz_images_new(mcq_s3_image_paths, book_id, chapter_id, res_id, mappings, explanation_mappings)
                    if extracted_img_urls:
                        all_extracted_img_urls.extend(extracted_img_urls)
            return all_extracted_img_urls

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error extracting quiz images: {e}")
            logger.error(traceback.format_exc())
            return []

    def _natural_sort_key(self, filename):
        """
        Generate a natural sort key for filenames containing numbers.
        This ensures proper ordering like: file1.png, file2.png, file10.png
        """
        import re
        return [int(text) if text.isdigit() else text.lower() for text in re.split(r'(\d+)', filename)]

    def _append_images_to_questions(self, questions, images_from_content, task_id):
        """
        Simple approach: Append image URLs to question text and options in HTML format.
        Based on Grails appendImgLinks method.

        Args:
            questions: List of question dictionaries
            images_from_content: List of extracted image URLs
            task_id: Task ID for logging

        Returns:
            List[Dict]: Updated questions with images appended to text
        """
        try:
            # Build CDN path based on config.CURRENT_ENV
            current_env = getattr(config, 'CURRENT_ENV', 'qa').lower()
            if current_env == 'publish':
                current_env = 'live'
            cdn_path = f"https://d1xcofdbxwssh7.cloudfront.net/{current_env}/supload/"

            # Deduplicate images_from_content first to avoid processing duplicates
            unique_images = list(set(images_from_content))

            # Create a simple mapping based on question numbers from filenames
            question_images = {}
            option_images = {}

            for image_url in unique_images:
                try:
                    filename = os.path.basename(image_url)

                    if filename.startswith('question_'):
                        # Extract question number from filename
                        name_parts = filename.replace('.png', '').split('_')
                        if len(name_parts) >= 2:
                            try:
                                question_num = int(name_parts[1])

                                if len(name_parts) == 2:
                                    # question_X.png - question image
                                    if question_num not in question_images:
                                        question_images[question_num] = []
                                    # Check for duplicates before adding
                                    if image_url not in question_images[question_num]:
                                        question_images[question_num].append(image_url)

                                elif len(name_parts) >= 3 and name_parts[2] == 'option':
                                    # question_X_option_Y.png - option image
                                    if question_num not in option_images:
                                        option_images[question_num] = []
                                    # Check for duplicates before adding
                                    if image_url not in option_images[question_num]:
                                        option_images[question_num].append(image_url)
                            except ValueError:
                                logger.debug(f"Could not parse question number from {filename}")
                                continue

                except Exception as e:
                    logger.error(f"Error processing image {image_url}: {e}")
                    continue

            # Log the mapping summary
            total_question_images = sum(len(imgs) for imgs in question_images.values())
            total_option_images = sum(len(imgs) for imgs in option_images.values())

            # Append images to questions
            updated_questions = []
            for question in questions:
                try:
                    question_number = question.get('question_number')
                    if question_number is not None:
                        try:
                            q_num = int(question_number)

                            # Append question images to question text
                            if q_num in question_images:
                                question_text = question.get('question', '')
                                original_length = len(question_text)
                                question_text = self._append_img_links(question_text, question_images[q_num], cdn_path)
                                question['question'] = question_text
                                images_added = len(question_text) > original_length

                            # Append option images to respective options
                            if q_num in option_images:
                                # For simplicity, append to first option
                                option1 = question.get('option1', '')
                                if option1:
                                    original_length = len(option1)
                                    question['option1'] = self._append_img_links(option1, option_images[q_num], cdn_path)
                                    images_added = len(question['option1']) > original_length
                        except (ValueError, TypeError):
                            logger.debug(f"Invalid question number format: {question_number}")

                    updated_questions.append(question)

                except Exception as e:
                    logger.error(f"[TASK:{task_id}] Error appending images to question: {e}")
                    updated_questions.append(question)
                    continue
            return updated_questions

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error in simple image appending: {e}")
            logger.error(traceback.format_exc())
            # Return original questions without images on error
            return questions

    def _append_img_links(self, text, images_list, cdn_path):
        """
        Append image links to text in HTML format.
        Based on Grails appendImgLinks method.

        Args:
            text: Original text
            images_list: List of image URLs
            cdn_path: CDN base path

        Returns:
            str: Text with appended image HTML
        """
        if len(images_list) > 0:
            for image_url in images_list:
                # Extract the path after 'source=' parameter
                if 'source=' in image_url:
                    extracted_url = image_url.split('source=', 1)[1]
                else:
                    # If no 'source=' parameter, use the full URL path after domain
                    extracted_url = image_url.replace('https://', '').split('/', 1)[1] if '/' in image_url else image_url

                # Create the image HTML tag
                img_html = f"\n<p><img src='{cdn_path}{extracted_url}' /></p>"

                # Check if this image is already present in the text to avoid duplicates
                if img_html not in text:
                    text += img_html

        return text
